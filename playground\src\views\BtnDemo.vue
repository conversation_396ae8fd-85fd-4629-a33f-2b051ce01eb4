<template>
  <div class="btn-demo">
    <div class="demo-header">
      <h1>🎯 Btn 按钮组件演示</h1>
      <p>展示新的按钮组件的各种变体、插槽功能和按钮组</p>
    </div>

    <!-- 基础变体 -->
    <section class="demo-section">
      <h2>📦 基础变体</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>Default 默认按钮</h3>
          <sp-btn
            variant="default"
            @click="handleClick('default')"
          >
            BUTTON
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>Outlined 边框按钮</h3>
          <sp-btn
            variant="outlined"
            @click="handleClick('outlined')"
          >
            BUTTON
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>Text 文本按钮</h3>
          <sp-btn
            variant="text"
            @click="handleClick('text')"
          >
            BUTTON
          </sp-btn>
        </div>
      </div>
    </section>

    <!-- 前置和后置插槽 -->
    <section class="demo-section">
      <h2>🎨 前置和后置插槽</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>前置图标</h3>
          <sp-btn @click="handleClick('prepend-icon')">
            <template #prepend-icon>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </template>
            收藏
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>后置图标</h3>
          <sp-btn variant="outlined" @click="handleClick('append-icon')">
            下一步
            <template #append-icon>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
              </svg>
            </template>
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>只有图标</h3>
          <sp-btn variant="text" @click="handleClick('icon-only')">
            <template #prepend-icon>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </template>
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>前置+后置</h3>
          <sp-btn @click="handleClick('both-icons')">
            <template #prepend-icon>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
              </svg>
            </template>
            完成
            <template #append-icon>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
              </svg>
            </template>
          </sp-btn>
        </div>
      </div>
    </section>

    <!-- 尺寸变体 -->
    <section class="demo-section">
      <h2>📏 尺寸变体</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>Small 小尺寸</h3>
          <sp-btn
            variant="default"
            size="small"
            @click="handleClick('small')"
          >
            <template #prepend-icon>📝</template>
            BUTTON
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>Medium 中等尺寸</h3>
          <sp-btn
            variant="default"
            size="medium"
            @click="handleClick('medium')"
          >
            <template #prepend-icon>📝</template>
            BUTTON
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>Large 大尺寸</h3>
          <sp-btn
            variant="default"
            size="large"
            @click="handleClick('large')"
          >
            <template #prepend-icon>📝</template>
            BUTTON
          </sp-btn>
        </div>
      </div>
    </section>

    <!-- 按钮组 - 水平 -->
    <section class="demo-section">
      <h2>🔗 按钮组 - 水平排列</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>基础按钮组</h3>
          <sp-btn-group direction="horizontal">
            <sp-btn @click="handleClick('group-left')">左侧</sp-btn>
            <sp-btn @click="handleClick('group-center')">中间</sp-btn>
            <sp-btn @click="handleClick('group-right')">右侧</sp-btn>
          </sp-btn-group>
        </div>

        <div class="demo-item">
          <h3>带图标的按钮组</h3>
          <sp-btn-group direction="horizontal" variant="outlined">
            <sp-btn @click="handleClick('group-edit')">
              <template #prepend-icon>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                </svg>
              </template>
              编辑
            </sp-btn>
            <sp-btn @click="handleClick('group-delete')">
              <template #prepend-icon>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                </svg>
              </template>
              删除
            </sp-btn>
            <sp-btn @click="handleClick('group-confirm')">
              <template #prepend-icon>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </template>
              确认
            </sp-btn>
          </sp-btn-group>
        </div>
      </div>
    </section>

    <!-- 按钮组 - 垂直 -->
    <section class="demo-section">
      <h2>📐 按钮组 - 垂直排列</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>垂直按钮组</h3>
          <sp-btn-group direction="vertical">
            <sp-btn @click="handleClick('vertical-up')">
              <template #prepend-icon>⬆️</template>
              向上
            </sp-btn>
            <sp-btn @click="handleClick('vertical-center')">
              <template #prepend-icon>🔄</template>
              中间
            </sp-btn>
            <sp-btn @click="handleClick('vertical-down')">
              <template #prepend-icon>⬇️</template>
              向下
            </sp-btn>
          </sp-btn-group>
        </div>

        <div class="demo-item">
          <h3>文本变体组</h3>
          <sp-btn-group direction="vertical" variant="text">
            <sp-btn @click="handleClick('text-1')">文本按钮1</sp-btn>
            <sp-btn @click="handleClick('text-2')">文本按钮2</sp-btn>
            <sp-btn @click="handleClick('text-3')">文本按钮3</sp-btn>
          </sp-btn-group>
        </div>
      </div>
    </section>

    <!-- 状态演示 -->
    <section class="demo-section">
      <h2>🎭 状态演示</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>正常状态</h3>
          <sp-btn
            variant="default"
            @click="handleClick('normal')"
          >
            <template #prepend-icon>🚫</template>
            BUTTON
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>禁用按钮</h3>
          <sp-btn
            variant="default"
            disabled
            @click="handleClick('disabled')"
          >
            <template #prepend-icon>🚫</template>
            BUTTON
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>禁用按钮组</h3>
          <sp-btn-group disabled>
            <sp-btn @click="handleClick('group-disabled-1')">禁用组1</sp-btn>
            <sp-btn @click="handleClick('group-disabled-2')">禁用组2</sp-btn>
            <sp-btn @click="handleClick('group-disabled-3')">禁用组3</sp-btn>
          </sp-btn-group>
        </div>
      </div>
    </section>

    <!-- 组合演示 -->
    <section class="demo-section">
      <h2>🎨 组合演示</h2>
      <div class="demo-grid">
        <!-- Default 变体的所有尺寸 -->
        <div class="demo-group">
          <h3>Default 变体</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="default"
              size="small"
            >
              Small
            </sp-btn>
            <sp-btn
              variant="default"
              size="medium"
            >
              Medium
            </sp-btn>
            <sp-btn
              variant="default"
              size="large"
            >
              Large
            </sp-btn>
          </div>
        </div>

        <!-- Outlined 变体的所有尺寸 -->
        <div class="demo-group">
          <h3>Outlined 变体</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="outlined"
              size="small"
            >
              Small
            </sp-btn>
            <sp-btn
              variant="outlined"
              size="medium"
            >
              Medium
            </sp-btn>
            <sp-btn
              variant="outlined"
              size="large"
            >
              Large
            </sp-btn>
          </div>
        </div>

        <!-- Text 变体的所有尺寸 -->
        <div class="demo-group">
          <h3>Text 变体</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="text"
              size="small"
            >
              Small
            </sp-btn>
            <sp-btn
              variant="text"
              size="medium"
            >
              Medium
            </sp-btn>
            <sp-btn
              variant="text"
              size="large"
            >
              Large
            </sp-btn>
          </div>
        </div>

        <!-- 禁用状态 -->
        <div class="demo-group">
          <h3>禁用状态</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="default"
              disabled
            >
              Default
            </sp-btn>
            <sp-btn
              variant="outlined"
              disabled
            >
              Outlined
            </sp-btn>
            <sp-btn
              variant="text"
              disabled
            >
              Text
            </sp-btn>
          </div>
        </div>
      </div>
    </section>

    <!-- 交互演示 -->
    <section class="demo-section">
      <h2>🎮 交互演示</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>点击事件</h3>
          <sp-btn @click="handleClick('click-demo')">
            <template #prepend-icon>�</template>
            点击我
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>鼠标事件</h3>
          <sp-btn
            @mouseenter="handleMouseEnter"
            @mouseleave="handleMouseLeave"
            @click="handleClick('mouse-demo')"
          >
            <template #prepend-icon>🖱️</template>
            悬停我
          </sp-btn>
        </div>
      </div>
      <p v-if="message" class="message">{{ message }}</p>
    </section>

    <!-- 交互说明 -->
    <section class="demo-section">
      <h2>💡 功能特性</h2>
      <div class="interaction-info">
        <ul>
          <li>
            <strong>前置插槽</strong>
            ：使用 #prepend-icon 或 #prepend 插槽添加前置内容
          </li>
          <li>
            <strong>后置插槽</strong>
            ：使用 #append-icon 或 #append 插槽添加后置内容
          </li>
          <li>
            <strong>按钮组</strong>
            ：支持水平和垂直排列，自动处理边框圆角
          </li>
          <li>
            <strong>状态管理</strong>
            ：通过 provide/inject 实现嵌套状态共享
          </li>
          <li>
            <strong>双层架构</strong>
            ：外层负责样式，内层负责逻辑，职责分离
          </li>
        </ul>
      </div>
    </section>

    <!-- 点击日志 -->
    <section class="demo-section">
      <h2>📝 点击日志</h2>
      <div class="click-log">
        <div
          v-if="clickLog.length === 0"
          class="no-clicks"
        >
          暂无点击记录，点击上面的按钮试试！
        </div>
        <div v-else>
          <div
            v-for="(log, index) in clickLog"
            :key="index"
            class="log-item"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-action">点击了</span>
            <span class="log-target">{{ log.target }}</span>
            <span class="log-button">按钮</span>
          </div>
        </div>
        <button
          v-if="clickLog.length > 0"
          @click="clearLog"
          class="clear-log"
        >
          清空日志
        </button>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  // 注意：sp-btn 和 sp-btn-group 组件已经通过 SpeedUI 插件全局注册，无需手动导入

  // 点击日志
  interface ClickLog {
    time: string
    target: string
  }

  const clickLog = ref<ClickLog[]>([])
  const message = ref('')

  const handleClick = (target: string) => {
    const now = new Date()
    const time = now.toLocaleTimeString()

    clickLog.value.unshift({
      time,
      target,
    })

    // 限制日志数量
    if (clickLog.value.length > 10) {
      clickLog.value = clickLog.value.slice(0, 10)
    }
  }

  const handleMouseEnter = () => {
    message.value = '鼠标悬停中...'
  }

  const handleMouseLeave = () => {
    message.value = '鼠标离开了'
    setTimeout(() => {
      message.value = ''
    }, 1000)
  }

  const clearLog = () => {
    clickLog.value = []
  }
</script>

<style scoped>
  .btn-demo {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .demo-header {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid #f0f0f0;
  }

  .demo-header h1 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 2.5rem;
  }

  .demo-header p {
    color: #7f8c8d;
    font-size: 1.2rem;
  }

  .demo-section {
    margin-bottom: 3rem;
  }

  .demo-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #ecf0f1;
    font-size: 1.5rem;
  }

  .demo-row {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
  }

  .demo-item {
    flex: 1;
    min-width: 200px;
    padding: 1.5rem;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    background: #fafbfc;
    text-align: center;
  }

  .demo-item h3 {
    color: #34495e;
    margin-bottom: 1rem;
    font-size: 1.1rem;
  }

  .demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
  }

  .demo-group {
    padding: 1.5rem;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    background: #fafbfc;
  }

  .demo-group h3 {
    color: #34495e;
    margin-bottom: 1rem;
    text-align: center;
    font-size: 1.1rem;
  }

  .demo-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    align-items: center;
  }

  .interaction-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
  }

  .interaction-info ul {
    margin: 0;
    padding-left: 1.5rem;
  }

  .interaction-info li {
    margin-bottom: 0.5rem;
    color: #495057;
  }

  .click-log {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    min-height: 100px;
  }

  .no-clicks {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 2rem;
  }

  .log-item {
    display: flex;
    gap: 0.5rem;
    padding: 0.5rem;
    margin-bottom: 0.25rem;
    background: white;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
  }

  .log-time {
    color: #6c757d;
    font-weight: bold;
  }

  .log-action {
    color: #495057;
  }

  .log-target {
    color: #007bff;
    font-weight: bold;
  }

  .log-button {
    color: #495057;
  }

  .clear-log {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
  }

  .clear-log:hover {
    background: #c82333;
  }

  .message {
    margin-top: 16px;
    padding: 12px;
    background: #e8f5e8;
    border: 1px solid #4caf50;
    border-radius: 4px;
    color: #2e7d32;
    font-weight: 500;
    text-align: center;
  }
</style>
