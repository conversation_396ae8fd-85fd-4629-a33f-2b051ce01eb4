<template>
  <div
    :class="buttonClasses"
    :style="buttonStyles"
  >
    <BtnBase
      ref="btnBaseRef"
      :disabled="disabled"
      v-bind="$attrs"
    >
      <!-- 前置插槽 -->
      <template v-if="$slots.prepend || $slots['prepend-icon']">
        <span :class="bem.e('prepend')">
          <slot name="prepend">
            <slot name="prepend-icon" />
          </slot>
        </span>
      </template>

      <!-- 主要内容 -->
      <span v-if="$slots.default" :class="bem.e('content')">
        <slot />
      </span>

      <!-- 后置插槽 -->
      <template v-if="$slots.append || $slots['append-icon']">
        <span :class="bem.e('append')">
          <slot name="append">
            <slot name="append-icon" />
          </slot>
        </span>
      </template>
    </BtnBase>
  </div>
</template>

<script setup lang="ts">
  import { ref, toRef } from 'vue'
  import BtnBase from './btn-base.vue'
  import { useBtnState } from '../../composables/useBtnState'
  import { provideBtnContext, createBtnContext } from '../../composables/useBtnContext'
  import type { BtnStyleProps } from './types'

  // 禁用属性继承，手动控制
  defineOptions({
    inheritAttrs: false
  })

  // 样式层组件，负责样式计算和类名生成
  const props = withDefaults(defineProps<BtnStyleProps>(), {
    variant: 'default',
    disabled: false,
    size: 'medium',
    isHovered: false,
    isPressed: false,
  })

  // 获取逻辑层组件的引用（保持向后兼容）
  const btnBaseRef = ref()

  // 使用按钮状态管理 composable
  const { buttonClasses, buttonStyles, currentIsHovered, currentIsPressed, bem } = useBtnState({
    props,
    btnBaseRef
  })

  // 创建并提供按钮上下文
  const btnContext = createBtnContext({
    isHovered: currentIsHovered,
    isPressed: currentIsPressed,
    disabled: toRef(props, 'disabled'),
    variant: props.variant,
    size: props.size,
  })

  provideBtnContext(btnContext)
</script>
