<template>
  <div
    :class="buttonClasses"
    :style="buttonStyles"
  >
    <BtnBase
      ref="btnBaseRef"
      :disabled="disabled"
      v-bind="$attrs"
    >
      <slot />
    </BtnBase>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import BtnBase from './btn-base.vue'
  import { useBtnState } from '../../composables/useBtnState'
  import type { BtnStyleProps } from './types'

  // 禁用属性继承，手动控制
  defineOptions({
    inheritAttrs: false
  })

  // 样式层组件，负责样式计算和类名生成
  const props = withDefaults(defineProps<BtnStyleProps>(), {
    variant: 'default',
    disabled: false,
    size: 'medium',
    isHovered: false,
    isPressed: false,
  })

  // 获取逻辑层组件的引用（保持向后兼容）
  const btnBaseRef = ref()

  // 使用按钮状态管理 composable
  const { buttonClasses, buttonStyles } = useBtnState({
    props,
    btnBaseRef
  })
</script>
