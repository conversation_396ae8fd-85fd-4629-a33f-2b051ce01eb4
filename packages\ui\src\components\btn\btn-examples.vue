<template>
  <div class="btn-examples">
    <h2>按钮组件示例</h2>
    
    <!-- 基础按钮 -->
    <section>
      <h3>基础按钮</h3>
      <div class="example-row">
        <Btn>默认按钮</Btn>
        <Btn variant="outlined">边框按钮</Btn>
        <Btn variant="text">文本按钮</Btn>
      </div>
    </section>

    <!-- 带图标的按钮 -->
    <section>
      <h3>带图标的按钮</h3>
      <div class="example-row">
        <!-- 前置图标 -->
        <Btn>
          <template #prepend-icon>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </template>
          前置图标
        </Btn>

        <!-- 后置图标 -->
        <Btn>
          后置图标
          <template #append-icon>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
            </svg>
          </template>
        </Btn>

        <!-- 只有图标 -->
        <Btn>
          <template #prepend-icon>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </template>
        </Btn>
      </div>
    </section>

    <!-- 不同尺寸 -->
    <section>
      <h3>不同尺寸</h3>
      <div class="example-row">
        <Btn size="small">小按钮</Btn>
        <Btn size="medium">中按钮</Btn>
        <Btn size="large">大按钮</Btn>
      </div>
    </section>

    <!-- 按钮组 - 水平 -->
    <section>
      <h3>按钮组 - 水平</h3>
      <div class="example-row">
        <BtnGroup direction="horizontal">
          <Btn>左</Btn>
          <Btn>中</Btn>
          <Btn>右</Btn>
        </BtnGroup>
      </div>
    </section>

    <!-- 按钮组 - 垂直 -->
    <section>
      <h3>按钮组 - 垂直</h3>
      <div class="example-row">
        <BtnGroup direction="vertical">
          <Btn>上</Btn>
          <Btn>中</Btn>
          <Btn>下</Btn>
        </BtnGroup>
      </div>
    </section>

    <!-- 按钮组 - 不同变体 -->
    <section>
      <h3>按钮组 - 不同变体</h3>
      <div class="example-row">
        <BtnGroup variant="outlined">
          <Btn>
            <template #prepend-icon>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
              </svg>
            </template>
            编辑
          </Btn>
          <Btn>
            <template #prepend-icon>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
              </svg>
            </template>
            删除
          </Btn>
          <Btn>
            <template #prepend-icon>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </template>
            确认
          </Btn>
        </BtnGroup>
      </div>
    </section>

    <!-- 禁用状态 -->
    <section>
      <h3>禁用状态</h3>
      <div class="example-row">
        <Btn disabled>禁用按钮</Btn>
        <BtnGroup disabled>
          <Btn>禁用组1</Btn>
          <Btn>禁用组2</Btn>
        </BtnGroup>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { Btn, BtnGroup } from './index'
</script>

<style scoped>
.btn-examples {
  padding: 20px;
  max-width: 800px;
}

section {
  margin-bottom: 30px;
}

h2 {
  color: #333;
  margin-bottom: 20px;
}

h3 {
  color: #666;
  margin-bottom: 15px;
  font-size: 16px;
}

.example-row {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}
</style>
