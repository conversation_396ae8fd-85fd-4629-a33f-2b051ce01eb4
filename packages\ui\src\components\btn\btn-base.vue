<template>
  <button
    :disabled="disabled"
    v-on="eventHandlers"
  >
    <slot />
  </button>
</template>

<script setup lang="ts">
  import { toRef, provide } from 'vue'
  import { useBaseEvents } from '../../composables/useBaseEvents'
  import type { BtnBaseProps, BtnEmits } from './types'

  // 逻辑层组件，负责状态管理和事件处理
  const props = withDefaults(defineProps<BtnBaseProps>(), {
    disabled: false,
  })

  const emit = defineEmits<BtnEmits>()

  // 使用基础事件处理 composable
  const { isHovered, isPressed, eventHandlers } = useBaseEvents({
    disabled: toRef(props, 'disabled'),
    emit,
  })

  // 通过 provide 向上层提供状态
  provide('btnState', {
    isHovered,
    isPressed,
    disabled: toRef(props, 'disabled')
  })

  // 暴露状态给父组件（保持向后兼容）
  defineExpose({
    isHovered,
    isPressed,
  })
</script>
