<template>
  <button
    :disabled="isDisabled"
    v-on="eventHandlers"
  >
    <slot />
  </button>
</template>

<script setup lang="ts">
  import { provide, computed } from 'vue'
  import { useBaseEvents } from '../../composables/useBaseEvents'

  // 逻辑层组件，负责状态管理和事件处理
  interface Props {
    disabled?: boolean
  }

  interface Emits {
    click: [event?: MouseEvent]
    mouseenter: []
    mouseleave: []
    mousedown: []
    mouseup: []
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 确保 disabled 有明确的值
  const isDisabled = computed(() => props.disabled === true)

  // 使用基础事件处理 composable
  const { isHovered, isPressed, eventHandlers } = useBaseEvents({
    disabled: isDisabled,
    emit,
  })

  // 通过 provide 向上层提供状态
  provide('btnState', {
    isHovered,
    isPressed,
    disabled: isDisabled
  })

  // 暴露状态给父组件（保持向后兼容）
  // defineExpose({
  //   isHovered,
  //   isPressed,
  // })
</script>
