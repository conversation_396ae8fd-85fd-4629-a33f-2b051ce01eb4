/**
 * 按钮状态管理 composable
 * 统一处理按钮的状态计算和样式生成
 */

import { computed, inject, type Ref } from 'vue'
import { bemHelper } from '@speed-ui/config'
import type { BtnStyleProps, BtnStateInject } from '../components/btn/types'

export interface UseBtnStateOptions {
  props: BtnStyleProps
  btnBaseRef?: Ref<any>
}

/**
 * 按钮状态管理 hook
 * 处理状态计算、类名生成和样式生成
 */
export function useBtnState(options: UseBtnStateOptions) {
  const { props, btnBaseRef } = options

  // 从子组件注入状态
  const btnState = inject<BtnStateInject | null>('btnState', null)

  // 创建 BEM 类名生成器
  const bem = bemHelper('btn')

  // 计算当前状态（优先级：props > inject > ref）
  const currentIsHovered = computed(() => 
    props.isHovered || 
    btnState?.isHovered?.value || 
    btnBaseRef?.value?.isHovered ||
    false
  )
  
  const currentIsPressed = computed(() => 
    props.isPressed || 
    btnState?.isPressed?.value || 
    btnBaseRef?.value?.isPressed ||
    false
  )

  // 计算类名
  const buttonClasses = computed(() => [
    bem.b(),
    bem.m(props.variant || 'default'),
    props.size === 'medium' ? '' : bem.m(props.size || 'medium'),
    {
      [bem.m('disabled')]: props.disabled,
      [bem.m('hovered')]: currentIsHovered.value,
      [bem.m('pressed')]: currentIsPressed.value,
    },
  ])

  // 计算样式
  const buttonStyles = computed(() => {
    const styles: Record<string, string> = {}

    // 根据状态添加样式变量
    if (currentIsHovered.value && !props.disabled) {
      styles['--btn-state'] = 'hovered'
    }
    if (currentIsPressed.value && !props.disabled) {
      styles['--btn-state'] = 'pressed'
    }

    return styles
  })

  return {
    // 状态
    currentIsHovered,
    currentIsPressed,
    // 样式
    buttonClasses,
    buttonStyles,
    // 工具
    bem,
  }
}
