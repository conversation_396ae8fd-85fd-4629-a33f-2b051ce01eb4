<template>
  <div class="btn-test">
    <h2>按钮组件测试</h2>
    
    <!-- 基础按钮 -->
    <section>
      <h3>基础按钮</h3>
      <div class="example-row">
        <Btn>默认按钮</Btn>
        <Btn variant="outlined">边框按钮</Btn>
        <Btn variant="text">文本按钮</Btn>
      </div>
    </section>

    <!-- 带图标的按钮 -->
    <section>
      <h3>带图标的按钮</h3>
      <div class="example-row">
        <!-- 前置图标 -->
        <Btn>
          <template #prepend-icon>
            ⭐
          </template>
          前置图标
        </Btn>

        <!-- 后置图标 -->
        <Btn>
          后置图标
          <template #append-icon>
            →
          </template>
        </Btn>

        <!-- 只有图标 -->
        <Btn>
          <template #prepend-icon>
            ✓
          </template>
        </Btn>
      </div>
    </section>

    <!-- 不同尺寸 -->
    <section>
      <h3>不同尺寸</h3>
      <div class="example-row">
        <Btn size="small">小按钮</Btn>
        <Btn size="medium">中按钮</Btn>
        <Btn size="large">大按钮</Btn>
      </div>
    </section>

    <!-- 按钮组 - 水平 -->
    <section>
      <h3>按钮组 - 水平</h3>
      <div class="example-row">
        <BtnGroup direction="horizontal">
          <Btn>左</Btn>
          <Btn>中</Btn>
          <Btn>右</Btn>
        </BtnGroup>
      </div>
    </section>

    <!-- 按钮组 - 垂直 -->
    <section>
      <h3>按钮组 - 垂直</h3>
      <div class="example-row">
        <BtnGroup direction="vertical">
          <Btn>上</Btn>
          <Btn>中</Btn>
          <Btn>下</Btn>
        </BtnGroup>
      </div>
    </section>

    <!-- 禁用状态 -->
    <section>
      <h3>禁用状态</h3>
      <div class="example-row">
        <Btn disabled>禁用按钮</Btn>
        <BtnGroup disabled>
          <Btn>禁用组1</Btn>
          <Btn>禁用组2</Btn>
        </BtnGroup>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { Btn, BtnGroup } from '@speed-ui/ui'
</script>

<style scoped>
.btn-test {
  padding: 20px;
  max-width: 800px;
}

section {
  margin-bottom: 30px;
}

h2 {
  color: #333;
  margin-bottom: 20px;
}

h3 {
  color: #666;
  margin-bottom: 15px;
  font-size: 16px;
}

.example-row {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}
</style>
