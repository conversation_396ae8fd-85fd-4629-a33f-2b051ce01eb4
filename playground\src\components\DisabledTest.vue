<template>
  <div class="disabled-test">
    <h2>Disabled 状态测试</h2>
    
    <div class="test-section">
      <h3>原生 button 测试</h3>
      <button disabled @click="handleClick('native-disabled')">原生禁用按钮</button>
      <button @click="handleClick('native-normal')">原生正常按钮</button>
    </div>

    <div class="test-section">
      <h3>sp-btn 测试</h3>
      <sp-btn disabled @click="handleClick('sp-btn-disabled')">SP禁用按钮</sp-btn>
      <sp-btn @click="handleClick('sp-btn-normal')">SP正常按钮</sp-btn>
    </div>

    <div class="test-section">
      <h3>动态测试</h3>
      <sp-btn :disabled="isDisabled" @click="handleClick('dynamic')">
        动态按钮 ({{ isDisabled ? '禁用' : '启用' }})
      </sp-btn>
      <button @click="toggleDisabled">切换状态</button>
    </div>

    <div class="test-section">
      <h3>点击日志</h3>
      <div class="log">
        <div v-if="clickLog.length === 0">暂无点击</div>
        <div v-for="(log, index) in clickLog" :key="index">
          {{ log.time }} - {{ log.target }}
        </div>
      </div>
      <button @click="clearLog">清空</button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  const isDisabled = ref(false)
  const clickLog = ref<Array<{ time: string; target: string }>>([])

  const handleClick = (target: string) => {
    const time = new Date().toLocaleTimeString()
    clickLog.value.unshift({ time, target })
    console.log('点击了:', target)
  }

  const toggleDisabled = () => {
    isDisabled.value = !isDisabled.value
    console.log('切换状态:', isDisabled.value)
  }

  const clearLog = () => {
    clickLog.value = []
  }
</script>

<style scoped>
.disabled-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.test-section > * {
  margin-right: 10px;
  margin-bottom: 10px;
}

.log {
  max-height: 150px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 10px;
  margin: 10px 0;
  font-family: monospace;
  font-size: 12px;
}
</style>
