/**
 * 按钮上下文管理 composable
 * 提供统一的按钮状态管理和通信机制
 */

import { provide, inject, type InjectionKey, type Ref } from 'vue'

// 按钮上下文类型
export interface BtnContext {
  // 状态
  isHovered: Ref<boolean>
  isPressed: Ref<boolean>
  disabled: Ref<boolean>
  
  // 配置
  variant?: string
  size?: string
  
  // 方法
  updateState?: (state: Partial<{ isHovered: boolean; isPressed: boolean }>) => void
}

// 注入键
export const BTN_CONTEXT_KEY: InjectionKey<BtnContext> = Symbol('btn-context')

/**
 * 提供按钮上下文
 * 在按钮容器组件中使用
 */
export function provideBtnContext(context: BtnContext) {
  provide(BTN_CONTEXT_KEY, context)
  return context
}

/**
 * 注入按钮上下文
 * 在按钮子组件中使用
 */
export function useBtnContext() {
  const context = inject(BTN_CONTEXT_KEY, null)
  
  return {
    context,
    hasContext: !!context,
  }
}

/**
 * 创建按钮上下文
 * 用于初始化按钮上下文数据
 */
export function createBtnContext(options: {
  isHovered: Ref<boolean>
  isPressed: Ref<boolean>
  disabled: Ref<boolean>
  variant?: string
  size?: string
}): BtnContext {
  const { isHovered, isPressed, disabled, variant, size } = options
  
  const updateState = (state: Partial<{ isHovered: boolean; isPressed: boolean }>) => {
    if (state.isHovered !== undefined) {
      isHovered.value = state.isHovered
    }
    if (state.isPressed !== undefined) {
      isPressed.value = state.isPressed
    }
  }
  
  return {
    isHovered,
    isPressed,
    disabled,
    variant,
    size,
    updateState,
  }
}
