// ================================
// Speed UI Btn 组件样式
// ================================

@use 'sass:map';
@use './common/var.scss' as *;

// Btn 组件样式已经在组件内部定义
// 这里可以添加全局主题变量覆盖或扩展样式

// 响应式设计支持
@media (max-width: 768px) {
  .sp-btn {
    // 在小屏幕上稍微增加触摸目标大小
    min-height: 36px;
    padding: 10px 18px;
  }

  .sp-btn.sp-btn--small {
    min-height: 28px;
    padding: 6px 14px;
  }

  .sp-btn.sp-btn--large {
    min-height: 44px;
    padding: 14px 26px;
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .sp-btn {
    border-width: 2px;
  }

  .sp-btn:focus-visible {
    outline-width: 3px;
  }
}

// 减少动画模式支持
@media (prefers-reduced-motion: reduce) {
  .sp-btn {
    transition: none;
  }
}

// 深色模式支持（如果需要）
@media (prefers-color-scheme: dark) {
  .sp-btn.sp-btn--outlined {
    background-color: rgba(255, 255, 255, 0.05);
  }

  .sp-btn.sp-btn--text {
    background-color: transparent;
  }

  .sp-btn.sp-btn--text:hover:not(:disabled) {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

.sp-btn {
  /* 基础样式重置 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: none;
  padding: 0;
  margin: 0;
  font: inherit;
  color: inherit;
  cursor: pointer;
  outline: none;
  text-decoration: none;
  box-sizing: border-box;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;

  /* 基础尺寸和样式 */
  min-height: 32px;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  /* 默认变体样式 */
  background-color: var(--sp-color-primary, #1890ff);
  color: #ffffff;
  border: 1px solid var(--sp-color-primary, #1890ff);

  // 确保使用全局主题变量
  --sp-color-primary: var(--sp-color-primary, #1890ff);
  --sp-color-primary-hover: var(--sp-color-primary-hover, #40a9ff);
  --sp-color-primary-active: var(--sp-color-primary-active, #096dd9);
  --sp-color-primary-lightest: var(--sp-color-primary-lightest, #f0f9ff);
  --sp-color-primary-light: var(--sp-color-primary-light, #e6f7ff);
}

/* 禁用状态 */
.sp-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* 默认变体 */
.sp-btn.sp-btn--default {
  background-color: var(--sp-color-primary, #1890ff);
  color: #ffffff;
  border: 1px solid var(--sp-color-primary, #1890ff);
}

.sp-btn.sp-btn--default:hover:not(:disabled),
.sp-btn.sp-btn--default.sp-btn--hovered:not(:disabled) {
  background-color: var(--sp-color-primary-hover, #40a9ff);
  border-color: var(--sp-color-primary-hover, #40a9ff);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.sp-btn.sp-btn--default:active:not(:disabled),
.sp-btn.sp-btn--default.sp-btn--pressed:not(:disabled) {
  background-color: var(--sp-color-primary-active, #096dd9);
  border-color: var(--sp-color-primary-active, #096dd9);
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.4);
}

/* Outlined 变体 */
.sp-btn.sp-btn--outlined {
  background-color: transparent;
  color: var(--sp-color-primary, #1890ff);
  border: 1px solid var(--sp-color-primary, #1890ff);
}

.sp-btn.sp-btn--outlined:hover:not(:disabled),
.sp-btn.sp-btn--outlined.sp-btn--hovered:not(:disabled) {
  background-color: var(--sp-color-primary-lightest, #f0f9ff);
  color: var(--sp-color-primary-hover, #40a9ff);
  border-color: var(--sp-color-primary-hover, #40a9ff);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.sp-btn.sp-btn--outlined:active:not(:disabled),
.sp-btn.sp-btn--outlined.sp-btn--pressed:not(:disabled) {
  background-color: var(--sp-color-primary-light, #e6f7ff);
  color: var(--sp-color-primary-active, #096dd9);
  border-color: var(--sp-color-primary-active, #096dd9);
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);
}

/* Text 变体 */
.sp-btn.sp-btn--text {
  background-color: transparent;
  color: var(--sp-color-primary, #1890ff);
  border: 1px solid transparent;
  padding: 4px 8px;
}

.sp-btn.sp-btn--text:hover:not(:disabled),
.sp-btn.sp-btn--text.sp-btn--hovered:not(:disabled) {
  background-color: var(--sp-color-primary-lightest, #f0f9ff);
  color: var(--sp-color-primary-hover, #40a9ff);
}

.sp-btn.sp-btn--text:active:not(:disabled),
.sp-btn.sp-btn--text.sp-btn--pressed:not(:disabled) {
  background-color: var(--sp-color-primary-light, #e6f7ff);
  color: var(--sp-color-primary-active, #096dd9);
}

/* 尺寸变体 */
.sp-btn.sp-btn--small {
  min-height: 24px;
  padding: 4px 12px;
  font-size: 12px;
  border-radius: 4px;
}

.sp-btn.sp-btn--medium {
  min-height: 32px;
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 6px;
}

.sp-btn.sp-btn--large {
  min-height: 40px;
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 8px;
}

/* 焦点样式 */
.sp-btn:focus-visible {
  outline: 2px solid var(--sp-color-primary, #1890ff);
  outline-offset: 2px;
}

/* 确保内容居中 */
.sp-btn > * {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 前置和后置插槽样式 */
.sp-btn__prepend {
  display: inline-flex;
  align-items: center;
  margin-right: 6px;

  &:empty {
    display: none;
  }
}

.sp-btn__content {
  display: inline-flex;
  align-items: center;
  flex: 1;

  &:empty {
    display: none;
  }
}

.sp-btn__append {
  display: inline-flex;
  align-items: center;
  margin-left: 6px;

  &:empty {
    display: none;
  }
}

/* 图标样式优化 */
.sp-btn__prepend,
.sp-btn__append {
  .sp-icon {
    font-size: 1em;
    line-height: 1;
  }

  svg {
    width: 1em;
    height: 1em;
    fill: currentColor;
  }
}

/* 只有图标的按钮 */
.sp-btn:has(.sp-btn__prepend):not(:has(.sp-btn__content)):not(:has(.sp-btn__append)) .sp-btn__prepend,
.sp-btn:has(.sp-btn__append):not(:has(.sp-btn__content)):not(:has(.sp-btn__prepend)) .sp-btn__append {
  margin: 0;
}

/* 尺寸变体的图标调整 */
.sp-btn--small {
  .sp-btn__prepend {
    margin-right: 4px;
  }

  .sp-btn__append {
    margin-left: 4px;
  }
}

.sp-btn--large {
  .sp-btn__prepend {
    margin-right: 8px;
  }

  .sp-btn__append {
    margin-left: 8px;
  }
}

/* ================================
   按钮组样式
   ================================ */

.sp-btn-group {
  display: inline-flex;
  position: relative;

  /* 水平排列 */
  &.sp-btn-group--horizontal {
    flex-direction: row;

    .sp-btn {
      border-radius: 0;

      &:first-child {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
      }

      &:last-child {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }

      &:not(:first-child) {
        margin-left: -1px;
      }

      &:hover,
      &.sp-btn--hovered {
        z-index: 1;
      }

      &:focus,
      &:active,
      &.sp-btn--pressed {
        z-index: 2;
      }
    }
  }

  /* 垂直排列 */
  &.sp-btn-group--vertical {
    flex-direction: column;

    .sp-btn {
      border-radius: 0;
      width: 100%;

      &:first-child {
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
      }

      &:last-child {
        border-bottom-left-radius: 6px;
        border-bottom-right-radius: 6px;
      }

      &:not(:first-child) {
        margin-top: -1px;
      }

      &:hover,
      &.sp-btn--hovered {
        z-index: 1;
      }

      &:focus,
      &:active,
      &.sp-btn--pressed {
        z-index: 2;
      }
    }
  }

  /* 尺寸变体 */
  &.sp-btn-group--small {
    .sp-btn {
      &:first-child,
      &:last-child {
        border-radius: 4px;
      }
    }

    &.sp-btn-group--horizontal .sp-btn {
      &:first-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }

      &:last-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }

    &.sp-btn-group--vertical .sp-btn {
      &:first-child {
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
      }

      &:last-child {
        border-top-left-radius: 0;
        border-top-right-radius: 0;
      }
    }
  }

  &.sp-btn-group--large {
    .sp-btn {
      &:first-child,
      &:last-child {
        border-radius: 8px;
      }
    }

    &.sp-btn-group--horizontal .sp-btn {
      &:first-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }

      &:last-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }

    &.sp-btn-group--vertical .sp-btn {
      &:first-child {
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
      }

      &:last-child {
        border-top-left-radius: 0;
        border-top-right-radius: 0;
      }
    }
  }

  /* 禁用状态 */
  &.sp-btn-group--disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}
