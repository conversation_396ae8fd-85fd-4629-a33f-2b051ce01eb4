<template>
  <div
    :class="groupClasses"
    :style="groupStyles"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
  import { computed, toRef } from 'vue'
  import { bemHelper } from '@speed-ui/config'
  import { provideBtnContext, createBtnContext } from '../../composables/useBtnContext'

  // 按钮组属性
  interface Props {
    direction?: 'horizontal' | 'vertical'
    size?: 'small' | 'medium' | 'large'
    variant?: 'default' | 'outlined' | 'text'
    disabled?: boolean
    gap?: number
  }

  const props = defineProps<Props>()

  // 创建 BEM 类名生成器
  const bem = bemHelper('btn-group')

  // 计算类名
  const groupClasses = computed(() => [
    bem.b(),
    bem.m(props.direction || 'horizontal'),
    bem.m(props.size || 'medium'),
    {
      [bem.m('disabled')]: props.disabled,
    },
  ])

  // 计算样式
  const groupStyles = computed(() => {
    const styles: Record<string, string> = {}

    if (props.gap && props.gap > 0) {
      styles['gap'] = `${props.gap}px`
    }

    return styles
  })

  // 为子按钮提供组上下文
  const groupContext = createBtnContext({
    isHovered: computed(() => false),
    isPressed: computed(() => false),
    disabled: toRef(props, 'disabled'),
    variant: props.variant || 'default',
    size: props.size || 'medium',
  })

  // 扩展上下文，添加组特有的属性
  const extendedContext = {
    ...groupContext,
    isInGroup: true,
    groupDirection: props.direction || 'horizontal',
    groupVariant: props.variant || 'default',
    groupSize: props.size || 'medium',
    groupDisabled: props.disabled || false,
  }

  provideBtnContext(extendedContext)
</script>
