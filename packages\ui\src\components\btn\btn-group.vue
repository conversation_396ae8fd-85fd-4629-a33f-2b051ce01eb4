<template>
  <div
    :class="groupClasses"
    :style="groupStyles"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
  import { computed, provide, toRef } from 'vue'
  import { bemHelper } from '@speed-ui/config'
  import { provideBtnContext, createBtnContext } from '../../composables/useBtnContext'
  import type { BtnGroupProps } from './types'

  // 禁用属性继承
  defineOptions({
    inheritAttrs: false
  })

  // 按钮组属性
  const props = withDefaults(defineProps<BtnGroupProps>(), {
    direction: 'horizontal',
    size: 'medium',
    variant: 'default',
    disabled: false,
    gap: 0,
  })

  // 创建 BEM 类名生成器
  const bem = bemHelper('btn-group')

  // 计算类名
  const groupClasses = computed(() => [
    bem.b(),
    bem.m(props.direction),
    bem.m(props.size),
    {
      [bem.m('disabled')]: props.disabled,
    },
  ])

  // 计算样式
  const groupStyles = computed(() => {
    const styles: Record<string, string> = {}
    
    if (props.gap > 0) {
      styles['gap'] = `${props.gap}px`
    }
    
    return styles
  })

  // 为子按钮提供组上下文
  const groupContext = createBtnContext({
    isHovered: computed(() => false),
    isPressed: computed(() => false),
    disabled: toRef(props, 'disabled'),
    variant: props.variant,
    size: props.size,
  })

  // 扩展上下文，添加组特有的属性
  const extendedContext = {
    ...groupContext,
    isInGroup: true,
    groupDirection: props.direction,
    groupVariant: props.variant,
    groupSize: props.size,
    groupDisabled: props.disabled,
  }

  provideBtnContext(extendedContext)

  // 暴露组实例方法
  defineExpose({
    // 可以添加组级别的方法
  })
</script>
