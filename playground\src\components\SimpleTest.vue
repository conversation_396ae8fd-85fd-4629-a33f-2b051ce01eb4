<template>
  <div class="simple-test">
    <h2>禁用状态测试</h2>

    <div>
      <h3>原生按钮对比</h3>
      <button @click="log('原生正常')">原生正常</button>
      <button disabled @click="log('原生禁用')">原生禁用</button>
      <button :disabled="true" @click="log('原生禁用true')">原生禁用(:disabled="true")</button>
    </div>

    <div>
      <h3>SP按钮测试</h3>
      <sp-btn @click="log('SP正常')" class="test-normal">SP正常</sp-btn>
      <sp-btn disabled @click="log('SP禁用')" class="test-disabled">SP禁用</sp-btn>
      <sp-btn :disabled="true" @click="log('SP禁用true')" class="test-disabled-true">SP禁用(true)</sp-btn>
      <sp-btn :disabled="false" @click="log('SP启用false')" class="test-enabled-false">SP启用(false)</sp-btn>
    </div>

    <div>
      <h3>动态测试</h3>
      <sp-btn :disabled="isDis" @click="log('动态')" class="test-dynamic">
        动态 {{ isDis ? '禁用' : '启用' }}
      </sp-btn>
      <button @click="toggleState">切换状态</button>
      <p>当前状态: {{ isDis }}</p>
    </div>

    <div>
      <h3>按钮组对比</h3>
      <sp-btn-group>
        <sp-btn @click="log('组正常1')">组正常1</sp-btn>
        <sp-btn @click="log('组正常2')">组正常2</sp-btn>
      </sp-btn-group>
      <br><br>
      <sp-btn-group disabled>
        <sp-btn @click="log('组禁用1')">组禁用1</sp-btn>
        <sp-btn @click="log('组禁用2')">组禁用2</sp-btn>
      </sp-btn-group>
    </div>

    <div>
      <h3>调试信息</h3>
      <button @click="checkClasses">检查CSS类名</button>
      <div v-if="debugInfo" class="debug">
        <pre>{{ debugInfo }}</pre>
      </div>
    </div>

    <div>
      <h3>点击日志</h3>
      <div v-for="(item, i) in logs" :key="i" class="log-item">{{ item }}</div>
      <button @click="logs = []">清空日志</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const isDis = ref(false)
const logs = ref<string[]>([])
const debugInfo = ref('')

const log = (msg: string) => {
  logs.value.unshift(`${new Date().toLocaleTimeString()} - ${msg}`)
}

const toggleState = () => {
  isDis.value = !isDis.value
  log(`切换为${isDis.value ? '禁用' : '启用'}`)
}

const checkClasses = () => {
  const elements = [
    { name: '正常按钮', selector: '.test-normal' },
    { name: '禁用按钮', selector: '.test-disabled' },
    { name: '禁用(true)', selector: '.test-disabled-true' },
    { name: '启用(false)', selector: '.test-enabled-false' },
    { name: '动态按钮', selector: '.test-dynamic' },
  ]

  let info = '按钮CSS类名检查:\n\n'

  elements.forEach(({ name, selector }) => {
    const el = document.querySelector(selector)
    if (el) {
      info += `${name}:\n`
      info += `  类名: ${el.className}\n`
      info += `  样式: opacity=${getComputedStyle(el).opacity}, pointer-events=${getComputedStyle(el).pointerEvents}\n\n`
    }
  })

  debugInfo.value = info
}
</script>

<style scoped>
.simple-test > div {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.simple-test button, .simple-test .sp-btn {
  margin: 5px;
}

.log-item {
  font-family: monospace;
  font-size: 12px;
  padding: 2px 0;
}

.debug {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  margin-top: 10px;
  font-size: 12px;
}

.debug pre {
  margin: 0;
  white-space: pre-wrap;
}
</style>
