<template>
  <div class="simple-test">
    <h2>简单测试</h2>
    
    <div>
      <h3>原生按钮</h3>
      <button @click="log('原生正常')">原生正常</button>
      <button disabled @click="log('原生禁用')">原生禁用</button>
    </div>
    
    <div>
      <h3>SP按钮</h3>
      <sp-btn @click="log('SP正常')">SP正常</sp-btn>
      <sp-btn disabled @click="log('SP禁用')">SP禁用</sp-btn>
      <sp-btn :disabled="true" @click="log('SP禁用true')">SP禁用(true)</sp-btn>
      <sp-btn :disabled="false" @click="log('SP启用false')">SP启用(false)</sp-btn>
    </div>
    
    <div>
      <h3>动态测试</h3>
      <sp-btn :disabled="isDis" @click="log('动态')">动态 {{ isDis ? '禁用' : '启用' }}</sp-btn>
      <button @click="isDis = !isDis">切换</button>
    </div>
    
    <div>
      <h3>日志</h3>
      <div v-for="(item, i) in logs" :key="i">{{ item }}</div>
      <button @click="logs = []">清空</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const isDis = ref(false)
const logs = ref<string[]>([])

const log = (msg: string) => {
  logs.value.unshift(`${new Date().toLocaleTimeString()} - ${msg}`)
}
</script>

<style scoped>
.simple-test > div {
  margin: 20px 0;
  padding: 10px;
  border: 1px solid #ccc;
}

.simple-test button, .simple-test .sp-btn {
  margin: 5px;
}
</style>
